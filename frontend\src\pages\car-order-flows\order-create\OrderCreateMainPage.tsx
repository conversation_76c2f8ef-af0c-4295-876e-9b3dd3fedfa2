import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import {
  CoraNCOBaseApiResponse,
  CoraNCOCreateApiRequest,
  FrontendNCOCreateQueryParams,
} from '../../../../../infrastructure/lib/types/new-car-order-types';
import { useAppDispatch, useAppSelector } from '../../../app/hooks';
import { routes } from '../../../Constants';
import { useCreateNewCarOrderMutation } from '../../../store/api/NewCarOrderApi';
import { useLazyGetQuotaForDealerQuery } from '../../../store/api/QuotaApi';
import { useGetStageConfigQuery } from '../../../store/api/StaticJsonApi';
import { selectKccConfig, setKccFocused } from '../../../store/slices/KccConfigSlice';
import { displayNotification } from '../../../store/slices/NotificationSlice';
import { ObjectValidator } from '../../../utils/object-validation';
import { useInIframe } from '../../../utils/useInIframe';
import { getErrorTextFromFetchError } from '../../errors/FetchErrors';
import KccConfiguration from '../../kcc-component/KccConfiguration';
import { KccComponentInitialRequestParams } from '../../kcc-component/kccUtils';
import { OrderFlowModal } from '../order-flow-modal/OrderFlowModal';
import { FormApiNewCarOrderCreateRequest, OrderCreateForm } from './OrderCreateForm';
import { PButton, PButtonGroup, PLink, PModal } from '@porsche-design-system/components-react';
import { CreateNcoPayload, OneVmsEventKey } from '../../../../../infrastructure/lib/types/process-steering-types';
import { ProcessSteeringResultStateComponent } from '../../../components/shared/process-steering-components/order-action-common-modal/ProcessSteeringResultState';
import { CarOrderInList } from '../../../store/types';

const OrderCreateMainPage: React.FC = () => {
  const { t } = useTranslation();
  const objectValidator = new ObjectValidator<CoraNCOCreateApiRequest>('CoraNCOCreateApiRequest');
  const objectValidatorQueryParams = new ObjectValidator<FrontendNCOCreateQueryParams>('FrontendNCOCreateQueryParams');
  const dispatch = useAppDispatch();
  const isInIframe = useInIframe();
  const { data: stageConfig } = useGetStageConfigQuery(undefined);
  const [searchParams] = useSearchParams();
  const correlationId = useAppSelector((state) => state.correlation.correlationId);
  const [newCarOrderToCreate, setNewCarOrderToCreate] = useState<FormApiNewCarOrderCreateRequest | undefined>(
    undefined,
  );
  const [kccParams, setKccParams] = useState<KccComponentInitialRequestParams>();
  const [triggerQuotaQuery, quotaResult] = useLazyGetQuotaForDealerQuery();
  const kccConfig = useAppSelector(selectKccConfig);
  const [modalError, setModalError] = useState<string | undefined>(undefined);
  const [quotaDashboardUrl, setQuotaDashboardUrl] = useState<string>('');
  const [formError, setFormError] = useState<string>();
  const [
    createOrder,
    {
      isLoading: isCreatingOrder,
      isError: isOrderCreateError,
      isSuccess: isOrderCreateSuccess,
      data: orderCreateData,
      error: orderCreateError,
    },
  ] = useCreateNewCarOrderMutation();

  function validateSearchParams(): FrontendNCOCreateQueryParams | undefined {
    const _params: Record<string, string> = {};
    for (const [key, value] of searchParams.entries()) {
      // each 'entry' is a [key, value] tupple
      _params[key] = value;
    }
    const [params_validated, validation_errors] = objectValidatorQueryParams.validate(_params);
    if (params_validated === null) {
      console.error('validation failed', JSON.stringify(validation_errors));
      dispatch(
        displayNotification({
          title: t('search_params_validation_error'),
          msg: `${t('error')}: ${JSON.stringify(validation_errors)}`,
          state: 'error',
        }),
      );
      return undefined;
    } else {
      return params_validated;
    }
  }

  function validateNewCarOrderRequest(order: Partial<CreateNcoPayload>): CreateNcoPayload | undefined {
    if (formError) {
      setModalError(formError);
      console.error('validation failed', formError);
      return undefined;
    }
    const [order_validated, validation_errors] = objectValidator.validate(order);
    if (order_validated === null) {
      console.error('validation failed', JSON.stringify(validation_errors));
      setModalError(validation_errors?.[0].message);
      return undefined;
    }
    setModalError(undefined);
    return order_validated;
  }

  function onFormChange(order: FormApiNewCarOrderCreateRequest) {
    setNewCarOrderToCreate(order);
  }

  function onCreateOrderClick() {
    if (newCarOrderToCreate) {
      const order = newCarOrderToCreate;
      order.configuration_signature = kccConfig.signature_config;
      order.configuration = kccConfig.config;
      order.configuration_expire = kccConfig.config_pvms;
      order.configuration_expire_signature = kccConfig.signature_config_pvms;
      const validated_order = validateNewCarOrderRequest(order);
      if (validated_order) {
        createOrder({ payload: validated_order });
        console.debug('Send Order Request', validated_order);
      }
    } else {
      dispatch(
        displayNotification({
          title: t('unknown_error'),
          msg: t('unknown_error', { area: 'order_create' }),
          state: 'error',
        }),
      );
    }
  }

  function restartKccWorkflow() {
    console.debug('reload kcc iframe');
  }

  useEffect(() => {
    if (isOrderCreateError && orderCreateError) {
      setModalError(getErrorTextFromFetchError(orderCreateError, t));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOrderCreateError]);

  useEffect(() => {
    const _keys = validateSearchParams();
    if (!_keys) {
      return;
    }
    const nco: FormApiNewCarOrderCreateRequest = {
      ..._keys,
    };
    triggerQuotaQuery({
      dealer_number: nco.dealer_number,
      model_type: nco.model_type,
      model_year: nco.model_year,
      importer_number: nco.importer_number,
    });
    setKccParams({ ...nco, process: 'CO1' });
    setNewCarOrderToCreate(nco);
  }, [searchParams]);

  useEffect(() => {
    if (!quotaResult.data) {
      return;
    }
    if (
      quotaResult.data.find(
        (q) => q.quota_month === searchParams.get('quota_month') && (q.quota_open > 0 || isOrderCreateSuccess),
      )
    ) {
      return;
    }
    dispatch(
      displayNotification({
        msg: `selected_quota_not_available: ${searchParams.get('quota_month')}`,
        state: 'error',
        title: 'quota_err',
        showUntil: new Date().getTime() + 60000,
      }),
    );
  }, [quotaResult]);

  const navigate = useNavigate();
  function goToOrderList() {
    dispatch(setKccFocused(true));
    navigate(routes.lists.preProductionOrders);
  }

  useEffect(() => {
    const quotaDashboardAppPath = isInIframe
      ? `${document.referrer}${routes.external_urls.quotaDashboardRelativePaddockPath}`
      : `${routes.external_urls.quotaDashboardFullPath(stageConfig?.stage)}`;
    setQuotaDashboardUrl(quotaDashboardAppPath);
  }, [stageConfig?.stage, isInIframe]);

  const getQuotaDashboardUrl = (): string => {
    const quotaDashboardAppPath = isInIframe
      ? `${document.referrer}${routes.external_urls.quotaDashboardRelativePaddockPath}`
      : `${routes.external_urls.quotaDashboardFullPath(stageConfig?.stage)}`;
    return quotaDashboardAppPath;
  };

  const getDoriUrl = (ncoId: string): string => {
    const doriOrderDetailsPath = routes.external_urls.doriOrderDetailsRelativePath(ncoId);
    const doriAppPath = isInIframe
      ? `${document.referrer}${routes.external_urls.doriRelativePaddockPath}`
      : `${routes.external_urls.doriFullPath(stageConfig?.stage)}`;
    return doriAppPath + doriOrderDetailsPath;
  };

  const navigationButtonGroup = (
    <PButtonGroup className="footer">
      <PLink data-e2e="go_to_quota_btn" href={getQuotaDashboardUrl()} target={'_top'}>
        {t('navigate_to_quota_dashboard')}
      </PLink>
      <PButton data-e2e="go_to_orders_btn" onClick={goToOrderList} variant="secondary">
        {t('navigate_to_order_list')} TEST
      </PButton>
    </PButtonGroup>
  );

  return (
    <div>
      {isOrderCreateSuccess && orderCreateData ? (
        <PModal
          disableBackdropClick
          data-e2e={`${OneVmsEventKey.CREATE}_order_modal`}
          open
          backdrop="blur"
          dismissButton={false}
          onDismiss={goToOrderList}
        >
          <ProcessSteeringResultStateComponent
            isMulti={false}
            closeModal={goToOrderList}
            result={orderCreateData}
            error={orderCreateError}
            actionType={OneVmsEventKey.CREATE}
            navigationButtonGroup={!orderCreateError ? navigationButtonGroup : undefined}
          />
        </PModal>
      ) : (
        <>
          {newCarOrderToCreate && (
            <OrderFlowModal
              e2eId="create_order_modal"
              heading={t('create_order')}
              error={modalError}
              body={
                <OrderCreateForm
                  partialNewCarOrder={newCarOrderToCreate}
                  onFormChange={onFormChange}
                  setError={setFormError}
                />
              }
              onConfirm={onCreateOrderClick}
              onBackToKcc={restartKccWorkflow}
              confirmButtonText="create_order"
              mutationRequestInProgress={isCreatingOrder}
            />
          )}
        </>
      )}
      {kccParams && (
        <KccConfiguration kccInitRequestParams={kccParams} kccCancelDefaultRedirectUrl={quotaDashboardUrl} />
      )}
    </div>
  );
};
export default OrderCreateMainPage;
